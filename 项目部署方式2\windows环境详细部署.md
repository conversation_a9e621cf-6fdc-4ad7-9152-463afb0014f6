# XXTT2 Laravel项目 - Windows环境详细部署指南

## 📋 部署概述

本文档记录了将XXTT2 Laravel项目从Docker Linux环境迁移到纯Windows开发环境的完整过程。

### 🎯 迁移目标
- **从**: Docker + Linux + Redis + Laravel Horizon
- **到**: 纯Windows + 小皮面板 + MySQL + 文件缓存
- **原因**: 消除ext-pcntl扩展依赖，简化开发环境

---

## 🛠️ 环境要求

### 必需软件
- **PHP**: 8.2.9+ (NTS版本)
- **Composer**: 2.8.10+
- **Node.js**: 20.18.3+
- **MySQL**: 通过小皮面板提供
- **小皮面板**: phpstudy_pro

### 用户环境信息
```
PHP版本: 8.2.9 (NTS)
PHP路径: C:\phpstudy_pro\Extensions\php\php8.2.9nts\php.exe
Composer版本: 2.8.10
Node.js版本: 20.18.3
数据库: MySQL (小皮面板)
```

---

## 🚀 详细部署步骤

### 第一步：移除Docker依赖

#### 1.1 删除Docker相关文件
```bash
# 删除的文件列表
- docker-compose.yml
- docker-compose.db.yml
- Dockerfile
- scripts/setup-windows.ps1
- scripts/start-windows-dev.ps1
- scripts/stop-windows-dev.ps1
- README-Windows.md
- README-简单版.md
- 项目MD文档/Windows开发环境迁移方案.md
```

#### 1.2 移除Laravel Horizon依赖
**问题**: Laravel Horizon需要ext-pcntl扩展，Windows不支持

**解决方案**: 从composer.json中移除
```json
// 删除这一行
"laravel/horizon": "^5.24"
```

### 第二步：解决Composer依赖冲突

#### 2.1 Stripe版本冲突
**问题**: `stripe/stripe-php ^16.3` 版本不存在

**解决方案**: 降级到可用版本
```json
// 修改 composer.json
"stripe/stripe-php": "^13.0"  // 从 ^16.3 改为 ^13.0
```

#### 2.2 PHP版本兼容性问题
**问题**: `openspout/openspout` 要求PHP 8.3+，但用户使用PHP 8.2.9

**解决方案**: 使用强制忽略平台要求
```bash
composer install --no-dev --ignore-platform-req=php
```

#### 2.3 成功安装依赖
```bash
# 最终成功的安装命令
composer install --no-dev --ignore-platform-req=php
```

### 第三步：配置PHP扩展

#### 3.1 启用必需的PHP扩展
**编辑文件**: `C:\phpstudy_pro\Extensions\php\php8.2.9nts\php.ini`

**添加/取消注释以下扩展**:
```ini
extension=curl
extension=mbstring
extension=openssl
extension=intl
extension=fileinfo
extension=exif
extension=zip
extension=pdo_mysql
extension=mysqli
```

**⚠️ 重要**: 不要添加 `extension=pdo`，PDO是内置的！

#### 3.2 修复Laravel Artisan Serve问题
**问题**: `php artisan serve` 报错 "Failed to listen on 127.0.0.1:8000 (reason: ?)"

**解决方案**: 添加variables_order配置
```ini
# 在php.ini的[PHP]部分添加
variables_order = "GPCS"
```

**重启**: 修改php.ini后必须重启小皮面板的PHP服务

### 第四步：数据库配置

#### 4.1 MySQL连接配置
**数据库信息**:
```
主机: 127.0.0.1
端口: 3306
数据库: lin2web_cms
用户名: root
密码: root
```

#### 4.2 环境配置文件
**创建 .env 文件** (从 .env.windows 复制):
```bash
copy .env.windows .env
```

**关键配置**:
```ini
# 数据库配置
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=lin2web_cms
DB_USERNAME=root
DB_PASSWORD=root

# 缓存配置 (使用文件缓存)
CACHE_DRIVER=file
CACHE_STORE=file

# 会话配置
SESSION_DRIVER=file

# 队列配置
QUEUE_CONNECTION=sync

# 禁用Redis
REDIS_CLIENT=
REDIS_HOST=
REDIS_PASSWORD=
REDIS_PORT=
REDIS_DB=
```

### 第五步：Laravel应用配置

#### 5.1 生成应用密钥
```bash
php artisan key:generate
```

#### 5.2 数据库迁移
```bash
# 测试数据库连接并运行迁移
php artisan migrate
```

#### 5.3 创建存储链接
```bash
php artisan storage:link
```

#### 5.4 清除缓存
```bash
php artisan config:clear
php artisan cache:clear
```

### 第六步：前端资源配置

#### 6.1 安装Node.js依赖
```bash
npm install
```

### 第七步：启动开发服务器

#### 7.1 启动Laravel服务器
```bash
php artisan serve
```

**成功输出**:
```
Starting Laravel development server: http://127.0.0.1:8000
[日期时间] PHP 8.2.9 Development Server (http://127.0.0.1:8000) started
```

---

## ⚠️ 常见问题与解决方案

### 问题1: PDO扩展错误
**错误**: `Warning: PHP Startup: Unable to load dynamic library 'pdo'`

**解决方案**: 从php.ini中删除 `extension=pdo` 行，PDO是内置的

### 问题2: Redis类未找到
**错误**: `未找到redis类`

**解决方案**:
1. 确保 `.env` 中 `CACHE_DRIVER=file`
2. 清除配置缓存: `php artisan config:clear`
3. 设置空的Redis配置变量

### 问题3: 端口被占用
**错误**: `Failed to listen on 127.0.0.1:8000`

**解决方案**: 在php.ini中添加 `variables_order = "GPCS"`

### 问题4: Composer依赖冲突
**错误**:
- `stripe/stripe-php ^16.3` 版本不存在
- `openspout/openspout` 要求PHP 8.3+

**解决方案**:
1. 降级stripe版本到 `^13.0`
2. 使用 `--ignore-platform-req=php` 强制安装

### 问题5: Vite构建失败
**错误**: `Could not resolve entry module "public/template/default/css/app.css"`

**解决方案**: 修改 `vite.config.js` 中的input配置，使用实际存在的文件：
```javascript
input: [
    'public/template/default/css/lin2web.css',
    'public/template/default/css/public.css',
    'public/template/default/css/auth.css',
    'public/template/default/css/swiper-bundle.min.css',
    'public/template/default/css/nouislider.min.css',
    'public/template/default/js/app.js',
    'public/template/default/js/custom.js',
    'public/template/default/js/swiper-bundle.min.js',
    'public/template/default/js/nouislider.min.js',
],
```

### 问题6: SQL Server驱动缺失
**错误**: `could not find driver` (Connection: lin2db)

**解决方案**: 修改 `config/app.php` 使其读取环境变量：
```php
// 修改前
'l2server_type' => 'pts',

// 修改后
'l2server_type' => env('L2SERVER_TYPE', 'pts'),
```

然后在 `.env` 中设置：
```ini
L2SERVER_TYPE=local  # 开发环境跳过SQL Server测试
GAME_SERVER_ENABLED=false
```

---

## 📁 最终项目结构

```
flyXxtt2/
├── app/                    # Laravel应用代码
├── config/                 # 配置文件
├── database/              # 数据库迁移和种子
├── public/                # 公共资源
├── resources/             # 视图和前端资源
├── routes/                # 路由定义
├── storage/               # 存储文件
├── vendor/                # Composer依赖
├── .env                   # 环境配置 (从.env.windows复制)
├── .env.windows           # Windows环境模板
├── composer.json          # PHP依赖配置
├── package.json           # Node.js依赖配置
└── 项目MD文档/            # 项目文档
```

---

## ✅ 部署验证清单

- [ ] PHP扩展正确启用 (`php -m` 检查)
- [ ] Composer依赖安装成功
- [ ] 数据库连接正常 (`php artisan migrate`)
- [ ] Laravel服务器启动成功 (`php artisan serve`)
- [ ] 网站可以正常访问 (`http://127.0.0.1:8000`)
- [ ] 无Redis相关错误
- [ ] 存储链接创建成功

---

## 🎉 部署完成

恭喜！XXTT2 Laravel项目已成功迁移到纯Windows开发环境。

**访问地址**: http://127.0.0.1:8000

**下一步**: 根据项目需要进行功能测试和开发工作。

---

*文档创建时间: 2025-07-31*
*迁移完成时间: 2025-07-31*
*环境: Windows + 小皮面板 + MySQL*
