# SQL Server PHP驱动安装指南

## 📋 概述

本指南帮助在Windows + 小皮面板环境中安装SQL Server PHP驱动，以支持游戏服务器数据库连接。

### 🎯 解决的问题
- `could not find driver` 错误
- 无法连接SQL Server数据库
- 游戏服务器数据库测试失败

---

## 🛠️ 安装步骤

### 第一步：安装Microsoft ODBC Driver 18 for SQL Server

#### 1.1 下载ODBC驱动
1. **访问下载页面**: https://go.microsoft.com/fwlink/?linkid=2249006
2. **下载文件**: `msodbcsql.msi`
3. **文件大小**: 约 5MB

#### 1.2 安装ODBC驱动
1. **右键点击** `msodbcsql.msi`
2. **选择** "以管理员身份运行"
3. **按照向导** 完成安装
4. **重启计算机** (推荐)

### 第二步：下载PHP SQL Server驱动

#### 2.1 下载驱动包
1. **访问下载页面**: https://go.microsoft.com/fwlink/?linkid=2258816
2. **下载文件**: `SQLSRV512.EXE` (约 15MB)
3. **解压文件**: 双击运行，选择解压目录

#### 2.2 找到适合的驱动文件
在解压目录中找到以下文件：
- `php_sqlsrv_82_nts_x64.dll`
- `php_pdo_sqlsrv_82_nts_x64.dll`

**注意**: 
- `82` = PHP 8.2版本
- `nts` = Non-Thread Safe版本
- `x64` = 64位版本

### 第三步：复制驱动文件

#### 3.1 复制到PHP扩展目录
将下载的两个DLL文件复制到：
```
C:\phpstudy_pro\Extensions\php\php8.2.9nts\ext\
```

**复制的文件**:
- `php_sqlsrv_82_nts_x64.dll`
- `php_pdo_sqlsrv_82_nts_x64.dll`

### 第四步：配置php.ini

#### 4.1 编辑php.ini文件
**文件位置**: `C:\phpstudy_pro\Extensions\php\php8.2.9nts\php.ini`

#### 4.2 添加扩展配置
在扩展部分添加以下行：
```ini
; SQL Server扩展
extension=php_sqlsrv_82_nts_x64
extension=php_pdo_sqlsrv_82_nts_x64
```

**建议位置**: 在 `extension=pdo_mysql` 后面添加

#### 4.3 保存文件
保存php.ini文件并关闭编辑器。

### 第五步：重启服务

#### 5.1 重启PHP服务
1. **打开小皮面板**
2. **找到PHP 8.2.9**
3. **点击重启按钮**
4. **等待重启完成**

#### 5.2 重启Web服务器 (可选)
1. **重启Apache服务**
2. **确保所有服务正常运行**

---

## ✅ 验证安装

### 检查扩展是否加载
运行以下命令检查：
```cmd
php -m | findstr sqlsrv
```

**期望输出**:
```
pdo_sqlsrv
sqlsrv
```

### 测试数据库连接
访问测试页面：
```
http://127.0.0.1:8000/db-test
```

**期望结果**: 游戏服务器数据库连接状态显示为成功

---

## ⚠️ 常见问题

### 问题1: 找不到DLL文件
**错误**: `The specified module could not be found`

**解决方案**:
1. 确认ODBC驱动已正确安装
2. 检查DLL文件是否在正确的ext目录中
3. 确认DLL文件版本与PHP版本匹配

### 问题2: 扩展加载失败
**错误**: `Unable to load dynamic library`

**解决方案**:
1. 检查php.ini中的扩展名是否正确
2. 确认文件权限正确
3. 重启PHP服务

### 问题3: 连接超时
**错误**: `Connection timeout expired`

**解决方案**:
1. 检查网络连接
2. 确认SQL Server服务器地址和端口
3. 检查防火墙设置

### 问题4: 认证失败
**错误**: `Login failed for user`

**解决方案**:
1. 检查用户名和密码
2. 确认SQL Server允许远程连接
3. 检查SQL Server认证模式

---

## 🔧 配置示例

### .env配置示例
```ini
# 游戏服务器配置
GAME_SERVER_ENABLED=true
L2SERVER_TYPE=pts

# PTS服务器配置
PTS_HOST=你的服务器IP
PTS_PORT=1433
PTS_DATABASE=lin2world
PTS_USERNAME=sa
PTS_PASSWORD=你的密码
```

### 数据库连接测试代码
```php
try {
    $pdo = new PDO(
        "sqlsrv:Server=服务器IP,1433;Database=lin2world",
        "用户名",
        "密码"
    );
    echo "连接成功！";
} catch (PDOException $e) {
    echo "连接失败: " . $e->getMessage();
}
```

---

## 📚 参考资源

### 官方文档
- [Microsoft Drivers for PHP for SQL Server](https://learn.microsoft.com/en-us/sql/connect/php/)
- [ODBC Driver for SQL Server](https://learn.microsoft.com/en-us/sql/connect/odbc/)

### 下载链接
- [ODBC Driver 18](https://go.microsoft.com/fwlink/?linkid=2249006)
- [PHP SQL Server Drivers](https://go.microsoft.com/fwlink/?linkid=2258816)

---

## 🎉 完成

安装完成后，你的PHP环境将支持SQL Server连接，可以正常访问游戏服务器数据库。

**测试地址**: http://127.0.0.1:8000/db-test

---

*文档创建时间: 2025-07-31*  
*适用环境: Windows + 小皮面板 + PHP 8.2.9*
