# XXTT2 Laravel项目 - 生产环境Windows配置教程

## 📋 概述

本教程指导如何在全新的Windows服务器上部署XXTT2 Laravel项目，适用于生产环境或新的开发环境。

### 🎯 部署目标
- **环境**: 纯Windows服务器
- **Web服务器**: Apache/Nginx (通过小皮面板)
- **数据库**: MySQL
- **缓存**: 文件缓存
- **适用场景**: 生产环境、新开发环境

---

## 🛠️ 系统要求

### 硬件要求
- **CPU**: 2核心以上
- **内存**: 4GB以上 (推荐8GB)
- **硬盘**: 20GB以上可用空间
- **网络**: 稳定的互联网连接

### 软件要求
- **操作系统**: Windows 10/11 或 Windows Server 2019/2022
- **PHP**: 8.2.9+ (NTS版本)
- **MySQL**: 5.7+ 或 8.0+
- **Web服务器**: Apache 2.4+ 或 Nginx 1.18+
- **Node.js**: 18.0+ (推荐20.x LTS)

---

## 🚀 详细部署步骤

### 第一步：安装基础环境

#### 1.1 下载并安装小皮面板
1. **访问官网**: https://www.xp.cn/
2. **下载**: 小皮面板 (phpstudy_pro)
3. **安装**: 以管理员身份运行安装程序
4. **启动**: 安装完成后启动小皮面板

#### 1.2 配置PHP环境
1. **打开小皮面板**
2. **软件管理** → **PHP** → **安装PHP 8.2.9**
3. **设置为默认版本**
4. **启动PHP服务**

#### 1.3 配置MySQL数据库
1. **软件管理** → **MySQL** → **安装MySQL 8.0**
2. **启动MySQL服务**
3. **设置root密码** (建议使用强密码)
4. **创建项目数据库**:
   ```sql
   CREATE DATABASE lin2web_cms CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   ```

#### 1.4 配置Web服务器
1. **软件管理** → **Apache** → **安装Apache 2.4**
2. **启动Apache服务**
3. **配置端口** (默认80，可根据需要修改)

### 第二步：安装开发工具

#### 2.1 安装Composer
1. **下载**: https://getcomposer.org/download/
2. **运行安装程序**
3. **验证安装**:
   ```cmd
   composer --version
   ```

#### 2.2 安装Node.js
1. **下载**: https://nodejs.org/ (选择LTS版本)
2. **运行安装程序**
3. **验证安装**:
   ```cmd
   node --version
   npm --version
   ```

#### 2.3 安装Git (可选)
1. **下载**: https://git-scm.com/download/win
2. **安装**: 使用默认配置
3. **验证**:
   ```cmd
   git --version
   ```

### 第三步：配置PHP环境

#### 3.1 编辑PHP配置文件
**文件位置**: `C:\phpstudy_pro\Extensions\php\php8.2.9nts\php.ini`

**必需配置**:
```ini
[Date]
date.timezone=Asia/Shanghai

[PHP]
variables_order = "GPCS"
max_execution_time = 300
max_input_time = 60
max_input_vars = 3000
memory_limit = 256M
upload_max_filesize = 100M
post_max_size = 100M
max_file_uploads = 100
display_errors = Off  ; 生产环境设为Off
display_startup_errors = Off
log_errors = On
error_log = C:/phpstudy_pro/Extensions/php/php8.2.9nts.log
error_reporting = E_ALL & ~E_NOTICE
allow_url_fopen = On
allow_url_include = Off

extension_dir = "ext"

; 必需扩展
extension=curl
extension=mbstring
extension=openssl
extension=intl
extension=fileinfo
extension=exif
extension=zip
extension=pdo_mysql
extension=mysqli

; SQL Server扩展 (用于游戏服务器数据库连接)
extension=php_sqlsrv_82_nts_x64
extension=php_pdo_sqlsrv_82_nts_x64
```

**⚠️ 重要**:
- 不要添加 `extension=pdo`
- 生产环境要关闭错误显示
- 设置合适的时区
- SQL Server扩展需要单独安装驱动文件

#### 3.2 安装SQL Server驱动 (游戏服务器连接必需)

**⚠️ 重要**: 如果需要连接游戏服务器数据库，必须安装SQL Server驱动。

**第一步：安装Microsoft ODBC Driver 18 for SQL Server**
1. **下载**: https://go.microsoft.com/fwlink/?linkid=2249006
2. **安装**: 以管理员身份运行 `msodbcsql.msi`
3. **完成安装并重启计算机**

**第二步：安装PHP SQL Server驱动**
1. **下载**: https://go.microsoft.com/fwlink/?linkid=2258816
2. **解压**: 运行 `SQLSRV512.EXE` 并解压到临时目录
3. **复制文件**: 从解压目录中找到以下文件并复制到PHP扩展目录：
   - `php_sqlsrv_82_nts_x64.dll` → `C:\phpstudy_pro\Extensions\php\php8.2.9nts\ext\`
   - `php_pdo_sqlsrv_82_nts_x64.dll` → `C:\phpstudy_pro\Extensions\php\php8.2.9nts\ext\`

**验证安装**:
```cmd
php -m | findstr sqlsrv
```
应该显示：
```
pdo_sqlsrv
sqlsrv
```

#### 3.3 重启PHP服务
在小皮面板中重启PHP服务使配置生效。

### 第四步：部署项目代码

#### 4.1 获取项目源码
**方式一：从Git仓库克隆**
```cmd
cd C:\phpstudy_pro\WWW
git clone [你的仓库地址] xxtt2
cd xxtt2
```

**方式二：上传压缩包**
1. 将项目压缩包上传到服务器
2. 解压到 `C:\phpstudy_pro\WWW\xxtt2\`

#### 4.2 安装PHP依赖
```cmd
cd C:\phpstudy_pro\WWW\xxtt2
composer install --no-dev --optimize-autoloader --ignore-platform-req=php
```

**参数说明**:
- `--no-dev`: 不安装开发依赖
- `--optimize-autoloader`: 优化自动加载
- `--ignore-platform-req=php`: 忽略PHP版本要求

#### 4.3 安装前端依赖
```cmd
npm install --production
npm run build
```

**⚠️ 重要说明**:
- `npm run build` 只需要运行一次，构建完成后不需要保持Vite运行
- 只有在修改前端代码时才需要重新构建
- 生产环境不需要运行 `npm run dev`

### 第五步：配置应用环境

#### 5.1 创建环境配置文件
```cmd
copy .env.windows .env
```

#### 5.2 编辑.env文件
**生产环境配置**:
```ini
APP_NAME=XXTT2
APP_ENV=production
APP_DEBUG=false
APP_KEY=  # 稍后生成
APP_URL=http://你的域名.com

# 数据库配置
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=lin2web_cms
DB_USERNAME=root
DB_PASSWORD=你的MySQL密码

# 缓存配置
CACHE_DRIVER=file
CACHE_STORE=file
SESSION_DRIVER=file
QUEUE_CONNECTION=sync

# 禁用Redis
REDIS_CLIENT=
REDIS_HOST=
REDIS_PASSWORD=
REDIS_PORT=
REDIS_DB=

# 邮件配置 (根据实际情况配置)
MAIL_MAILER=smtp
MAIL_HOST=你的SMTP服务器
MAIL_PORT=587
MAIL_USERNAME=你的邮箱
MAIL_PASSWORD=你的邮箱密码
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS="noreply@你的域名.com"
MAIL_FROM_NAME="XXTT2"

# 游戏服务器配置 (根据实际情况)
GAME_SERVER_ENABLED=true
L2SERVER_TYPE=pts  # pts=SQL Server, java=MySQL

# PTS服务器配置 (SQL Server)
PTS_HOST=你的游戏服务器IP
PTS_PORT=1433
PTS_DATABASE=lin2world
PTS_USERNAME=sa
PTS_PASSWORD=你的数据库密码

# Java服务器配置 (MySQL) - 如果使用Java服务器
JAVA_HOST=你的游戏服务器IP
JAVA_PORT=3306
JAVA_DATABASE=l2jgs
JAVA_USERNAME=root
JAVA_PASSWORD=你的数据库密码

# 支付系统配置 (根据需要启用)
FREEKASSA_ENABLED=false
MORUNE_ENABLED=false
PRIMEPAYMENTS_ENABLED=false

# 其他配置
TELESCOPE_ENABLED=false
DEBUGBAR_ENABLED=false
```

#### 5.3 生成应用密钥
```cmd
php artisan key:generate
```

#### 5.4 设置文件权限
```cmd
# 设置存储目录权限
icacls storage /grant Everyone:(OI)(CI)F /T
icacls bootstrap\cache /grant Everyone:(OI)(CI)F /T
```

### 第六步：数据库初始化

#### 6.1 运行数据库迁移
```cmd
php artisan migrate --force
```

#### 6.2 创建存储链接
```cmd
php artisan storage:link
```

#### 6.3 优化应用
```cmd
# 缓存配置
php artisan config:cache

# 缓存路由
php artisan route:cache

# 缓存视图
php artisan view:cache

# 优化自动加载
composer dump-autoload --optimize
```

### 第七步：配置Web服务器

#### 7.1 创建虚拟主机
1. **打开小皮面板**
2. **网站** → **创建网站**
3. **配置信息**:
   - **域名**: 你的域名.com
   - **根目录**: `C:\phpstudy_pro\WWW\xxtt2\public`
   - **PHP版本**: 8.2.9

#### 7.2 配置Apache虚拟主机 (可选)
**编辑文件**: `C:\phpstudy_pro\Extensions\Apache2.4.39\conf\vhosts.conf`

```apache
<VirtualHost *:80>
    ServerName 你的域名.com
    DocumentRoot "C:/phpstudy_pro/WWW/xxtt2/public"

    <Directory "C:/phpstudy_pro/WWW/xxtt2/public">
        AllowOverride All
        Require all granted

        # Laravel URL重写规则
        RewriteEngine On
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteRule ^(.*)$ index.php [QSA,L]
    </Directory>

    # 错误日志
    ErrorLog "logs/xxtt2_error.log"
    CustomLog "logs/xxtt2_access.log" common
</VirtualHost>
```

#### 7.3 重启Web服务器
在小皮面板中重启Apache服务。

---

## 🔒 安全配置

### 安全建议
1. **更改默认端口**: 避免使用默认的80端口
2. **设置防火墙**: 只开放必要的端口
3. **定期更新**: 保持系统和软件最新
4. **备份数据**: 定期备份数据库和文件
5. **监控日志**: 定期检查错误日志

### 文件权限设置
```cmd
# 设置适当的文件权限
icacls C:\phpstudy_pro\WWW\xxtt2 /grant IIS_IUSRS:(OI)(CI)RX /T
icacls C:\phpstudy_pro\WWW\xxtt2\storage /grant IIS_IUSRS:(OI)(CI)F /T
icacls C:\phpstudy_pro\WWW\xxtt2\bootstrap\cache /grant IIS_IUSRS:(OI)(CI)F /T
```

---

## ⚠️ 常见问题与解决方案

### 问题1: Composer安装失败
**错误**: 依赖版本冲突

**解决方案**:
```cmd
# 清除Composer缓存
composer clear-cache

# 强制安装
composer install --no-dev --ignore-platform-req=php --no-scripts
```

### 问题2: 数据库连接失败
**错误**: `SQLSTATE[HY000] [2002] No connection could be made`

**解决方案**:
1. 检查MySQL服务是否启动
2. 验证数据库配置信息
3. 检查防火墙设置

### 问题3: 文件权限错误
**错误**: `Permission denied`

**解决方案**:
```cmd
# 重新设置权限
icacls storage /reset /T
icacls storage /grant Everyone:(OI)(CI)F /T
```

### 问题4: 前端资源404
**错误**: CSS/JS文件无法加载

**解决方案**:
1. 确保运行了 `npm run build`
2. 检查 `public/build` 目录是否存在
3. 验证虚拟主机配置

### 问题5: SQL Server驱动问题
**错误**: `could not find driver` (Connection: lin2db)

**解决方案**:
1. **检查驱动安装**:
   ```cmd
   php -m | findstr sqlsrv
   ```
2. **重新安装ODBC驱动**: 确保Microsoft ODBC Driver 18已正确安装
3. **检查DLL文件**: 确认以下文件存在于PHP扩展目录：
   - `php_sqlsrv_82_nts_x64.dll`
   - `php_pdo_sqlsrv_82_nts_x64.dll`
4. **重启PHP服务**: 在小皮面板中重启PHP服务

### 问题6: 游戏服务器连接超时
**错误**: `Connection timeout expired`

**解决方案**:
1. 检查网络连接和防火墙设置
2. 验证游戏服务器IP和端口
3. 确认SQL Server允许远程连接
4. 检查SQL Server认证模式

---

## ✅ 部署验证清单

### 基础环境检查
- [ ] PHP 8.2.9+ 安装并配置正确
- [ ] MySQL 8.0+ 安装并运行
- [ ] Apache/Nginx 安装并运行
- [ ] Composer 安装成功
- [ ] Node.js 安装成功

### 项目配置检查
- [ ] 项目代码部署到正确位置
- [ ] PHP依赖安装成功
- [ ] 前端资源编译成功
- [ ] .env文件配置正确
- [ ] 应用密钥生成成功

### 功能测试检查
- [ ] 网站可以正常访问
- [ ] MySQL数据库连接正常
- [ ] SQL Server驱动安装成功 (如果需要游戏服务器连接)
- [ ] 游戏服务器数据库连接正常 (如果启用)
- [ ] 数据库测试页面 `/db-test` 显示所有连接正常
- [ ] 用户注册/登录功能正常
- [ ] 文件上传功能正常
- [ ] 邮件发送功能正常 (如果配置)

### 安全检查
- [ ] 生产环境关闭调试模式
- [ ] 文件权限设置正确
- [ ] 敏感信息不在版本控制中
- [ ] 防火墙配置正确

---

## 🎉 部署完成

恭喜！XXTT2 Laravel项目已成功部署到Windows生产环境。

### 访问信息
- **网站地址**: http://你的域名.com
- **管理后台**: http://你的域名.com/admin
- **小皮面板**: http://127.0.0.1:9999

### 后续维护
1. **定期备份**: 设置自动备份计划
2. **监控性能**: 关注服务器性能指标
3. **更新维护**: 定期更新系统和依赖
4. **日志监控**: 定期检查错误日志

---

## 📞 技术支持

如果在部署过程中遇到问题，请：

1. **检查日志**: 查看PHP错误日志和Apache访问日志
2. **参考文档**: 查看Laravel官方文档
3. **社区支持**: 在相关技术社区寻求帮助

---

*文档创建时间: 2025-07-31*
*适用版本: XXTT2 Laravel 10.x*
*环境: Windows + 小皮面板 + MySQL*
