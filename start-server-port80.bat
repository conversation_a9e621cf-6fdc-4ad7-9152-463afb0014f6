@echo off
echo ========================================
echo 🚀 启动 Laravel 开发服务器 (端口 80)
echo ========================================
echo.

REM 检查是否以管理员身份运行
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ 检测到管理员权限
) else (
    echo ❌ 错误: 需要管理员权限才能使用80端口
    echo.
    echo 💡 解决方案:
    echo 1. 右键点击此文件
    echo 2. 选择 "以管理员身份运行"
    echo.
    pause
    exit /b 1
)

echo.
echo 🔧 清理缓存...
php artisan config:clear
php artisan cache:clear
php artisan route:clear
php artisan view:clear

echo.
echo 🌐 启动服务器在 http://localhost (端口 80)...
echo.
echo ⚠️  注意事项:
echo - 确保端口 80 没有被其他程序占用
echo - 如果有 IIS 或 Apache 运行，请先停止
echo - 按 Ctrl+C 可以停止服务器
echo.

REM 启动 Laravel 开发服务器在80端口
php artisan serve --host=0.0.0.0 --port=80

echo.
echo 👋 服务器已停止
pause
