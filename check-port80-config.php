<?php

/**
 * 端口80配置检查脚本
 * 
 * 检查系统是否准备好在80端口运行Laravel应用
 */

echo "\n" . str_repeat("=", 80);
echo "\n🔍 端口80配置检查";
echo "\n" . str_repeat("=", 80);

/**
 * 检查端口是否被占用
 */
function checkPortAvailability($port = 80) {
    echo "\n\n📡 检查端口 {$port} 可用性...";
    
    // Windows 系统检查端口占用
    if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
        $output = [];
        $return_var = 0;
        exec("netstat -an | findstr :{$port}", $output, $return_var);
        
        if (!empty($output)) {
            echo "\n⚠️  端口 {$port} 正在被占用:";
            foreach ($output as $line) {
                echo "\n   " . trim($line);
            }
            
            // 尝试找到占用端口的进程
            exec("netstat -ano | findstr :{$port}", $processOutput);
            if (!empty($processOutput)) {
                echo "\n\n🔍 占用端口的进程:";
                foreach ($processOutput as $line) {
                    if (preg_match('/\s+(\d+)$/', $line, $matches)) {
                        $pid = $matches[1];
                        exec("tasklist /FI \"PID eq {$pid}\" /FO CSV", $taskOutput);
                        if (count($taskOutput) > 1) {
                            $taskInfo = str_getcsv($taskOutput[1]);
                            echo "\n   PID: {$pid} - 进程: {$taskInfo[0]}";
                        }
                    }
                }
            }
            return false;
        } else {
            echo "\n✅ 端口 {$port} 可用";
            return true;
        }
    } else {
        // Linux/Mac 系统检查
        $result = shell_exec("lsof -i :{$port}");
        if ($result) {
            echo "\n⚠️  端口 {$port} 正在被占用:";
            echo "\n" . $result;
            return false;
        } else {
            echo "\n✅ 端口 {$port} 可用";
            return true;
        }
    }
}

/**
 * 检查管理员权限 (Windows)
 */
function checkAdminRights() {
    echo "\n\n🔐 检查管理员权限...";
    
    if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
        // 尝试创建一个需要管理员权限的操作
        $output = [];
        exec('net session 2>&1', $output, $return_var);
        
        if ($return_var === 0) {
            echo "\n✅ 检测到管理员权限";
            return true;
        } else {
            echo "\n❌ 需要管理员权限才能使用80端口";
            echo "\n💡 解决方案:";
            echo "\n   1. 以管理员身份运行命令提示符";
            echo "\n   2. 或者使用其他端口 (如 8080)";
            return false;
        }
    } else {
        // Linux/Mac 检查是否为 root 或有 sudo 权限
        if (posix_getuid() === 0) {
            echo "\n✅ 检测到 root 权限";
            return true;
        } else {
            echo "\n⚠️  非 root 用户，可能需要 sudo 权限使用80端口";
            return false;
        }
    }
}

/**
 * 检查Laravel配置
 */
function checkLaravelConfig() {
    echo "\n\n⚙️ 检查 Laravel 配置...";
    
    // 检查 .env 文件
    if (file_exists('.env')) {
        $envContent = file_get_contents('.env');
        
        // 检查 APP_URL
        if (preg_match('/APP_URL=(.+)/', $envContent, $matches)) {
            $appUrl = trim($matches[1]);
            echo "\n📋 当前 APP_URL: {$appUrl}";
            
            if (strpos($appUrl, ':8000') !== false) {
                echo "\n⚠️  APP_URL 仍然包含端口 8000";
                echo "\n💡 建议修改为: http://localhost";
            } elseif (strpos($appUrl, 'localhost') !== false && strpos($appUrl, ':') === false) {
                echo "\n✅ APP_URL 配置正确 (无端口号，默认80)";
            }
        }
        
        // 检查其他相关配置
        echo "\n\n📋 其他配置检查:";
        $configs = [
            'APP_ENV' => '应用环境',
            'APP_DEBUG' => '调试模式',
            'DB_CONNECTION' => '数据库连接'
        ];
        
        foreach ($configs as $key => $desc) {
            if (preg_match("/{$key}=(.+)/", $envContent, $matches)) {
                $value = trim($matches[1]);
                echo "\n   {$desc}: {$value}";
            }
        }
        
    } else {
        echo "\n❌ 未找到 .env 文件";
        return false;
    }
    
    return true;
}

/**
 * 检查防火墙设置 (Windows)
 */
function checkFirewallSettings() {
    echo "\n\n🛡️ 检查防火墙设置...";
    
    if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
        $output = [];
        exec('netsh advfirewall firewall show rule name="HTTP" 2>nul', $output);
        
        if (empty($output)) {
            echo "\n⚠️  未找到 HTTP (端口80) 防火墙规则";
            echo "\n💡 如果遇到连接问题，可能需要添加防火墙规则:";
            echo "\n   netsh advfirewall firewall add rule name=\"HTTP\" dir=in action=allow protocol=TCP localport=80";
        } else {
            echo "\n✅ 找到 HTTP 防火墙规则";
        }
    } else {
        echo "\n💡 Linux/Mac 系统，请手动检查 iptables 或防火墙设置";
    }
}

/**
 * 生成启动命令建议
 */
function generateStartupCommands() {
    echo "\n\n🚀 启动命令建议:";
    echo "\n" . str_repeat("-", 40);
    
    echo "\n\n1️⃣ 使用提供的批处理文件 (推荐):";
    echo "\n   start-server-port80.bat";
    
    echo "\n\n2️⃣ 使用 PowerShell 脚本:";
    echo "\n   .\\start-server-port80.ps1";
    
    echo "\n\n3️⃣ 手动命令:";
    echo "\n   php artisan serve --host=0.0.0.0 --port=80";
    
    echo "\n\n4️⃣ 如果80端口不可用，使用其他端口:";
    echo "\n   php artisan serve --host=0.0.0.0 --port=8080";
    echo "\n   (记得修改 .env 中的 APP_URL=http://localhost:8080)";
}

/**
 * 系统信息检查
 */
function checkSystemInfo() {
    echo "\n\n💻 系统信息:";
    echo "\n" . str_repeat("-", 40);
    
    echo "\n操作系统: " . PHP_OS;
    echo "\nPHP 版本: " . PHP_VERSION;
    echo "\n当前用户: " . get_current_user();
    echo "\n工作目录: " . getcwd();
    
    // 检查 Laravel 版本
    if (file_exists('artisan')) {
        echo "\n✅ 检测到 Laravel 项目";
        
        // 尝试获取 Laravel 版本
        $output = [];
        exec('php artisan --version 2>&1', $output);
        if (!empty($output)) {
            echo "\nLaravel 版本: " . trim($output[0]);
        }
    } else {
        echo "\n❌ 未检测到 Laravel 项目 (缺少 artisan 文件)";
    }
}

// 运行所有检查
echo "\n🚀 开始检查...";

checkSystemInfo();
$portAvailable = checkPortAvailability(80);
$hasAdminRights = checkAdminRights();
checkLaravelConfig();
checkFirewallSettings();
generateStartupCommands();

// 总结
echo "\n\n" . str_repeat("=", 80);
echo "\n📋 检查总结:";
echo "\n" . str_repeat("-", 40);

if ($portAvailable) {
    echo "\n✅ 端口 80 可用";
} else {
    echo "\n❌ 端口 80 被占用";
}

if ($hasAdminRights) {
    echo "\n✅ 具有管理员权限";
} else {
    echo "\n❌ 缺少管理员权限";
}

echo "\n\n🎯 建议:";
if ($portAvailable && $hasAdminRights) {
    echo "\n✅ 系统准备就绪，可以在80端口启动服务器";
    echo "\n🚀 运行: start-server-port80.bat";
} elseif (!$portAvailable) {
    echo "\n⚠️  建议使用其他端口，如 8080";
    echo "\n🚀 运行: php artisan serve --port=8080";
} elseif (!$hasAdminRights) {
    echo "\n⚠️  请以管理员身份运行";
    echo "\n🚀 或使用其他端口: php artisan serve --port=8080";
}

echo "\n\n" . str_repeat("=", 80);
echo "\n🎮 配置检查完成";
echo "\n" . str_repeat("=", 80);
echo "\n";
