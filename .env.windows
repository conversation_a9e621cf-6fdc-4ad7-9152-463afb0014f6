APP_NAME=XXTT2
APP_ENV=local
APP_DEBUG=true
APP_KEY=
APP_TIMEZONE=UTC
APP_URL=http://localhost:8000

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file
APP_MAINTENANCE_STORE=database

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

# Windows本地数据库连接 (小皮面板MySQL)
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=lin2web_cms
DB_USERNAME=root
DB_PASSWORD=root

SESSION_DRIVER=file
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync

# 缓存使用文件系统 (简单快速)
CACHE_STORE=file
CACHE_PREFIX=

# Redis配置 (如果安装了Redis可以启用)
# REDIS_CLIENT=phpredis
# REDIS_HOST=127.0.0.1
# REDIS_PASSWORD=null
# REDIS_PORT=6379
# REDIS_DB=0

# 邮件配置 (开发环境使用log驱动)
MAIL_MAILER=log
MAIL_HOST=127.0.0.1
MAIL_PORT=587
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="XXTT2 Local Dev"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

VITE_APP_NAME="${APP_NAME}"

# 游戏服务器配置 (开发环境禁用)
GAME_SERVER_ENABLED=false
L2SERVER_TYPE=local

# PTS服务器配置 (开发环境)
PTS_HOST=127.0.0.1
PTS_PORT=1433
PTS_DATABASE=lin2world
PTS_USERNAME=sa
PTS_PASSWORD=

# Java服务器配置 (开发环境)
JAVA_HOST=127.0.0.1
JAVA_PORT=3306
JAVA_DATABASE=l2jgs
JAVA_USERNAME=root
JAVA_PASSWORD=

# 支付系统配置 (开发环境禁用)
FREEKASSA_ENABLED=false
MORUNE_ENABLED=false
PRIMEPAYMENTS_ENABLED=false

# 其他开发配置
TELESCOPE_ENABLED=false
DEBUGBAR_ENABLED=true
CACHE_DRIVER=redis
QUEUE_FAILED_DRIVER=database-uuids

# Windows特定配置
WWWUSER=1000
WWWGROUP=1000
